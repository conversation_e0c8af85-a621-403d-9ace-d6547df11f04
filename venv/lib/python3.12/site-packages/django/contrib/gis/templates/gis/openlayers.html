{% load i18n l10n %}

<div id="{{ id }}_div_map" class="dj_map_wrapper">
    <div id="{{ id }}_map" class="dj_map"></div>
    {% if not disabled %}<span class="clear_features"><a href="">{% translate "Delete all Features" %}</a></span>{% endif %}
    {% if display_raw %}<p>{% translate "Debugging window (serialized value)" %}</p>{% endif %}
    <textarea id="{{ id }}" class="vSerializedField required" cols="150" rows="10" name="{{ name }}"
              {% if not display_raw %} hidden{% endif %}>{{ serialized }}</textarea>
    <script>
        {% block base_layer %}
            var base_layer = new ol.layer.Tile({
                source: new ol.source.XYZ({
                    attributions: "NASA Worldview",
                    maxZoom: 8,
                    url: "https://map1{a-c}.vis.earthdata.nasa.gov/wmts-webmerc/" +
                         "BlueMarble_ShadedRelief_Bathymetry/default/%7BTime%7D/" +
                         "GoogleMapsCompatible_Level8/{z}/{y}/{x}.jpg"
                })
            });
        {% endblock %}
        {% block options %}var options = {
            base_layer: base_layer,
            geom_name: '{{ geom_type }}',
            id: '{{ id }}',
            map_id: '{{ id }}_map',
            map_srid: {{ map_srid|unlocalize }},
            name: '{{ name }}'
        };
        {% endblock %}
        var {{ module }} = new MapWidget(options);
    </script>
</div>

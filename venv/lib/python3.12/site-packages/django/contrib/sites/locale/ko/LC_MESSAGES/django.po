# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON>uffe <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2024-10-07 18:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Korean (http://app.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Sites"
msgstr "사이트"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "도메인 이름은 공백이나 탭을 포함할 수 없습니다."

msgid "domain name"
msgstr "도메인명"

msgid "display name"
msgstr "표시명"

msgid "site"
msgstr "사이트"

msgid "sites"
msgstr "사이트들"

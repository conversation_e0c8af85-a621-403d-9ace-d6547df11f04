# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2024-10-07 19:22+0000\n"
"Last-Translator: Ain<PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Irish (http://app.transifex.com/django/django/language/ga/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ga\n"
"Plural-Forms: nplurals=5; plural=(n==1 ? 0 : n==2 ? 1 : n<7 ? 2 : n<11 ? 3 : "
"4);\n"

msgid "Content Types"
msgstr "Cineál Inneachair"

msgid "python model class name"
msgstr "píotón samhail aicme ainm"

msgid "content type"
msgstr "tíopa inneachar "

msgid "content types"
msgstr "tíopaI inneachair"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Ní bhaineann samhail leis an cineál inneachar %(ct_id)s"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Níl cineál ábhair %(ct_id)s réad %(obj_id)s ann"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Níl modh get_absolute_url() ag réada %(ct_name)s"

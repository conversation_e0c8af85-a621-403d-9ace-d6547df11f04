# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/contenttypes/apps.py:16
msgid "Content Types"
msgstr ""

#: contrib/contenttypes/models.py:135
msgid "python model class name"
msgstr ""

#: contrib/contenttypes/models.py:139
msgid "content type"
msgstr ""

#: contrib/contenttypes/models.py:140
msgid "content types"
msgstr ""

#: contrib/contenttypes/views.py:18
#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""

#: contrib/contenttypes/views.py:24
#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr ""

#: contrib/contenttypes/views.py:32
#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr ""

# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2015
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2015-2018,2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-12-04 09:22+0000\n"
"Last-Translator: Алексей Борискин <<EMAIL>>, 2015-2018,2023\n"
"Language-Team: Russian (http://app.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "PostgreSQL extensions"
msgstr "Расширения PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Элемент %(nth)s в массиве не прошёл проверку:"

msgid "Nested arrays must have the same length."
msgstr "Вложенные массивы должны иметь одинаковую длину."

msgid "Map of strings to strings/nulls"
msgstr ""
"Ассоциативный массив со строковыми ключами и строковыми или отсутствующими "
"значениями."

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Значение “%(key)s” не является строкой или null."

msgid "Could not load JSON data."
msgstr "Не удалось загрузить JSON-данные."

msgid "Input must be a JSON dictionary."
msgstr "Значение должно быть JSON-словарём."

msgid "Enter two valid values."
msgstr "Введите два правильных значения."

msgid "The start of the range must not exceed the end of the range."
msgstr "Начало диапазона не может превышать его предел."

msgid "Enter two whole numbers."
msgstr "Введите два целых числа."

msgid "Enter two numbers."
msgstr "Введите два числа."

msgid "Enter two valid date/times."
msgstr "Введите две правильные даты со временем."

msgid "Enter two valid dates."
msgstr "Введите две правильные даты."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Список содержит %(show_value)d элемент, однако количество элементов не "
"должно превышать %(limit_value)d."
msgstr[1] ""
"Список содержит %(show_value)d элемента, однако количество элементов не "
"должно превышать %(limit_value)d."
msgstr[2] ""
"Список содержит %(show_value)d элементов, однако количество элементов не "
"должно превышать %(limit_value)d."
msgstr[3] ""
"Список содержит %(show_value)d элементов, однако количество элементов не "
"должно превышать %(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Список содержит %(show_value)d элемент, однако количество элементов должно "
"быть не меньше %(limit_value)d."
msgstr[1] ""
"Список содержит %(show_value)d элемента, однако количество элементов должно "
"быть не меньше %(limit_value)d."
msgstr[2] ""
"Список содержит %(show_value)d элементов, однако количество элементов должно "
"быть не меньше %(limit_value)d."
msgstr[3] ""
"Список содержит %(show_value)d элементов, однако количество элементов должно "
"быть не меньше %(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Некоторые ключи пропущены: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr ""
"Некоторые из предоставленных ключей не входят в список известных ключей: "
"%(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Убедитесь, что верхняя граница диапазона не больше, чем %(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Убедитесь, что нижняя граница диапазона не меньше, чем %(limit_value)s."

# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2024-10-07 09:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Czech (http://app.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "PostgreSQL extensions"
msgstr "Rozšíření pro PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Položka č. %(nth)s v seznamu je neplatná:"

msgid "Nested arrays must have the same length."
msgstr "Vnořená pole musejí mít stejnou délku."

msgid "Map of strings to strings/nulls"
msgstr "Mapování řetězců na řetězce či hodnoty NULL"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Hodnota s klíčem \"%(key)s\" není řetězec ani NULL."

msgid "Could not load JSON data."
msgstr "Data typu JSON nelze načíst."

msgid "Input must be a JSON dictionary."
msgstr "Vstup musí být slovník formátu JSON."

msgid "Enter two valid values."
msgstr "Zadejte dvě platné hodnoty."

msgid "The start of the range must not exceed the end of the range."
msgstr "Počáteční hodnota rozsahu nemůže být vyšší než koncová hodnota."

msgid "Enter two whole numbers."
msgstr "Zadejte dvě celá čísla."

msgid "Enter two numbers."
msgstr "Zadejte dvě čísla."

msgid "Enter two valid date/times."
msgstr "Zadejte dvě platné hodnoty data nebo času."

msgid "Enter two valid dates."
msgstr "Zadejte dvě platná data."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Seznam obsahuje %(show_value)d položku, ale neměl by obsahovat více než "
"%(limit_value)d."
msgstr[1] ""
"Seznam obsahuje %(show_value)d položky, ale neměl by obsahovat více než "
"%(limit_value)d."
msgstr[2] ""
"Seznam obsahuje %(show_value)d položek, ale neměl by obsahovat více než "
"%(limit_value)d."
msgstr[3] ""
"Seznam obsahuje %(show_value)d položek, ale neměl by obsahovat více než "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Seznam obsahuje %(show_value)d položku, ale neměl by obsahovat méně než "
"%(limit_value)d."
msgstr[1] ""
"Seznam obsahuje %(show_value)d položky, ale neměl by obsahovat méně než "
"%(limit_value)d."
msgstr[2] ""
"Seznam obsahuje %(show_value)d položek, ale neměl by obsahovat méně než "
"%(limit_value)d."
msgstr[3] ""
"Seznam obsahuje %(show_value)d položek, ale neměl by obsahovat méně než "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Některé klíče chybí: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Byly zadány neznámé klíče: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr "Ujistěte se, že horní hranice rozsahu není větší než %(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr "Ujistěte se, že spodní hranice rozsahu není menší než %(limit_value)s."

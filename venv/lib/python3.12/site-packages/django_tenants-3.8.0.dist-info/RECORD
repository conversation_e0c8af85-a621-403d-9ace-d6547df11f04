django_tenants-3.8.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_tenants-3.8.0.dist-info/METADATA,sha256=8W9K7OwxDaAMImvRl7iaqPa4rNCBToAyxwybS7MXT7Y,11081
django_tenants-3.8.0.dist-info/RECORD,,
django_tenants-3.8.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants-3.8.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
django_tenants-3.8.0.dist-info/licenses/LICENSE,sha256=KWyBzEfzYh4THp9gfpY49LNdBIemdZjzXGo4kwl_7Ho,1024
django_tenants-3.8.0.dist-info/top_level.txt,sha256=GBL6He3xaj44ZliovamxcCjBBapoyltxjWXe3veu2Wk,15
django_tenants/__init__.py,sha256=enuwpvKWUI1TMd8-qqxpUIdra6VDRpjnggIfd5GwfQc,65
django_tenants/__pycache__/__init__.cpython-312.pyc,,
django_tenants/__pycache__/admin.cpython-312.pyc,,
django_tenants/__pycache__/apps.cpython-312.pyc,,
django_tenants/__pycache__/cache.cpython-312.pyc,,
django_tenants/__pycache__/clone.cpython-312.pyc,,
django_tenants/__pycache__/log.cpython-312.pyc,,
django_tenants/__pycache__/models.cpython-312.pyc,,
django_tenants/__pycache__/routers.cpython-312.pyc,,
django_tenants/__pycache__/signals.cpython-312.pyc,,
django_tenants/__pycache__/urlresolvers.cpython-312.pyc,,
django_tenants/__pycache__/utils.cpython-312.pyc,,
django_tenants/admin.py,sha256=TtGd-P3k8WS0-mzTYRIxronrpCqqiDFcGY1aQ7z6hSM,244
django_tenants/apps.py,sha256=VsImdLloRBEotHkJx5PkAwxM9CxDkXDNQMrwc_y_Sqs,2172
django_tenants/cache.py,sha256=co-SWF4I3pMvRTBKTwUrZ2-R7suPqpgWdl57XsFGcDQ,510
django_tenants/clone.py,sha256=Wqx1RNs1e82G5u-OKlgyQqwb2Db6g3MBPXKfXvZaaZ8,237360
django_tenants/files/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/files/__pycache__/__init__.cpython-312.pyc,,
django_tenants/files/__pycache__/storage.cpython-312.pyc,,
django_tenants/files/__pycache__/storages.cpython-312.pyc,,
django_tenants/files/storage.py,sha256=tmpRKoMQ-RVEGayxu3fK69sFr2Sa4jxZLn6oEuXbX-U,2759
django_tenants/files/storages.py,sha256=8cgYhTsyOJ2lakmkLllMQTim7TBgcNgiP9FHKNpf9v0,610
django_tenants/log.py,sha256=q0F3IwsoH2rJQt9zTd2dk8KUlp2O-YVjnok02AV9wWQ,474
django_tenants/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/management/__pycache__/__init__.cpython-312.pyc,,
django_tenants/management/commands/__init__.py,sha256=d6K7OJ9JV2GPQHlrcvKOZPab3SEzlRDRM9SWikRKn7Q,10121
django_tenants/management/commands/__pycache__/__init__.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/all_tenants_command.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/clone_tenant.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/collectstatic_schemas.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/create_domain.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/create_missing_schemas.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/create_tenant.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/create_tenant_superuser.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/delete_domain.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/delete_tenant.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/migrate.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/migrate_schemas.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/rename_schema.cpython-312.pyc,,
django_tenants/management/commands/__pycache__/tenant_command.cpython-312.pyc,,
django_tenants/management/commands/all_tenants_command.py,sha256=Yzf4zBV747-wLGcI8WTMOB8M_EZInibo5J12ucw-SnM,1964
django_tenants/management/commands/clone_tenant.py,sha256=fWcjCGICRTRV29uAWo-6w4c-M88sfxOiQu5dw2IULNk,6365
django_tenants/management/commands/collectstatic_schemas.py,sha256=HrvyLDz_q-Xplz_VsIyKxjEw0irvayLbrG7kTZesCms,1803
django_tenants/management/commands/create_domain.py,sha256=JSGS4R1iknTQ6FMw38d-Dba48AaEEJRnVicCUKbfUf8,3430
django_tenants/management/commands/create_missing_schemas.py,sha256=63wg2OHlA3nNCwZc9VTcrtNs3DfMRvF8JyeG31B34hw,694
django_tenants/management/commands/create_tenant.py,sha256=M1ORUTPtGzFKa5stC923xI54t99M61kBxSH2DdNR4j0,5142
django_tenants/management/commands/create_tenant_superuser.py,sha256=2K1bbdVtWnT8WLTlKBvHE680mgkteAZnWLY0AmF4sZA,180
django_tenants/management/commands/delete_domain.py,sha256=srC8uNzK_HBBpRwZ81WzkV5MJDjN1hmUy-tkjpOYWuo,2149
django_tenants/management/commands/delete_tenant.py,sha256=jnFAfpUNTP7wCw05MSmrVoshiXiJvrV54trLs6ZROPU,1927
django_tenants/management/commands/migrate.py,sha256=B37G1Y0X2XPSgTc1-A2SeqFYurOrUjM9OL7jat6G4D4,119
django_tenants/management/commands/migrate_schemas.py,sha256=vnqWCgbEs3gu-d2COKRPtZcRbmcuK9K5Jau-sVI-8TM,5965
django_tenants/management/commands/rename_schema.py,sha256=q50qsu6_wT7WeqO8JM5UPr9_VvnP5nm7JtkN6si8SHU,1508
django_tenants/management/commands/tenant_command.py,sha256=E-yXRvVeP78o8KFlnJKnja7Atiq_KOXMG5dDamE55Ck,2540
django_tenants/middleware/__init__.py,sha256=50t2Fh0XJVf7nTFsCLE2WGzwx9_JPOM7i6H8r6x2KOk,418
django_tenants/middleware/__pycache__/__init__.cpython-312.pyc,,
django_tenants/middleware/__pycache__/default.cpython-312.pyc,,
django_tenants/middleware/__pycache__/main.cpython-312.pyc,,
django_tenants/middleware/__pycache__/subfolder.cpython-312.pyc,,
django_tenants/middleware/__pycache__/suspicious.cpython-312.pyc,,
django_tenants/middleware/default.py,sha256=-RvKWVcx6eKDj3Y7Wa8QM_hn9y8-gtNnVev48y70v4I,1050
django_tenants/middleware/main.py,sha256=Zjhr_3-rD4nNKQaOXKeIR-bt6hxuH2KYm3LjJfTo7qU,4063
django_tenants/middleware/subfolder.py,sha256=OZRZBqxuJPa9ExklgvktHJvYuHvDfOnOszQxvu3y0jQ,3179
django_tenants/middleware/suspicious.py,sha256=lBPObcMeLaVxpF9gy-4KhYJ-tImrm1PBIXO1TvEH6_o,601
django_tenants/migration_executors/__init__.py,sha256=KTaw2FtnU-jw7whyffEM-QYdumBByTAC3Y4KlT7i2Bc,449
django_tenants/migration_executors/__pycache__/__init__.cpython-312.pyc,,
django_tenants/migration_executors/__pycache__/base.cpython-312.pyc,,
django_tenants/migration_executors/__pycache__/multiproc.cpython-312.pyc,,
django_tenants/migration_executors/__pycache__/standard.cpython-312.pyc,,
django_tenants/migration_executors/base.py,sha256=9oMuGgQySrYSK16EhZS0C-oxL_JEQcUyIAllr37leho,3210
django_tenants/migration_executors/multiproc.py,sha256=92fk25o6S1lSeGoeVsJx0CqDP2ab7X3m-wm8j9S0NmI,2857
django_tenants/migration_executors/standard.py,sha256=5AnJNj50yahsWfTwiaamFEiyzv-W0xybaMV6lLE_7-U,1000
django_tenants/models.py,sha256=Ev7D7MbX4XwRqAB-v_95SQFhw-cY_oik9dXvG7MHhpQ,10115
django_tenants/postgresql_backend/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/postgresql_backend/__pycache__/__init__.cpython-312.pyc,,
django_tenants/postgresql_backend/__pycache__/base.cpython-312.pyc,,
django_tenants/postgresql_backend/__pycache__/introspection.cpython-312.pyc,,
django_tenants/postgresql_backend/base.py,sha256=jGi_LfhnK9AQZHnLxdFDPjYqKyDnU2vryJq4p48BmMQ,7795
django_tenants/postgresql_backend/introspection.py,sha256=z_pCVWlBAYOwqcqAhB4jctWJdb5EaSZ3o1NQ68xf7F8,2283
django_tenants/routers.py,sha256=pUTZyKb1OwKQouXQN6ZhLNW4awY78-_wA-4_YslMboY,2394
django_tenants/signals.py,sha256=l4IVGdWqYtw_HcwSnI1oY2zqqBGFDPTbP85oBd8aYBE,1156
django_tenants/staticfiles/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/staticfiles/__pycache__/__init__.cpython-312.pyc,,
django_tenants/staticfiles/__pycache__/finders.cpython-312.pyc,,
django_tenants/staticfiles/__pycache__/storage.cpython-312.pyc,,
django_tenants/staticfiles/finders.py,sha256=OVFHUfNVN4Z2t-7-Pgid7rYc6KxAU2uSd_kx4DrDlVM,3552
django_tenants/staticfiles/storage.py,sha256=mlfojikphiHMxXg8bZyOy5xgYaE3IgWTlUTKpxyfWCY,3106
django_tenants/template/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/template/__pycache__/__init__.cpython-312.pyc,,
django_tenants/template/loaders/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/template/loaders/__pycache__/__init__.cpython-312.pyc,,
django_tenants/template/loaders/__pycache__/cached.cpython-312.pyc,,
django_tenants/template/loaders/__pycache__/filesystem.cpython-312.pyc,,
django_tenants/template/loaders/cached.py,sha256=88RU1Fd7-ZuMLPi1pJjOpWmiHqGaWCHyzhBIjfHPxi8,634
django_tenants/template/loaders/filesystem.py,sha256=1Yv75-R7aJzycAphozH0-vHQsZECieNCwoG4bvafnVc,1504
django_tenants/templates/admin/app_list.html,sha256=ACNtzBPLFf4Yas69Jng5jJN4fHcxJg2fVKFQOlgRqrw,3032
django_tenants/templates/admin/django_tenants/tenant/change_form.html,sha256=nNW-PZEDoMzyur9jcuAFA2Nk15z--DXj3GEdrIKTRx4,655
django_tenants/templates/admin/index.html,sha256=-h8UaZGEsHNEiMSTi4eroReLtI2V3U8ordfyL1fIkIU,3860
django_tenants/templatetags/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/templatetags/__pycache__/__init__.cpython-312.pyc,,
django_tenants/templatetags/__pycache__/tenant.cpython-312.pyc,,
django_tenants/templatetags/tenant.py,sha256=jSVcS5G6kjz4VPepZu8Uh6jF1IdS-I_jwfbjaf1HISo,1841
django_tenants/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/test/__pycache__/__init__.cpython-312.pyc,,
django_tenants/test/__pycache__/cases.cpython-312.pyc,,
django_tenants/test/__pycache__/client.cpython-312.pyc,,
django_tenants/test/cases.py,sha256=X34VT3a0mVr5IBq7amYslV4HNT635jSiF2C23cysUYw,5308
django_tenants/test/client.py,sha256=zYXJVPKFS9O90u7ozmQQXGKe64o38986P-BaIRi3WnU,1385
django_tenants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_tenants/tests/__pycache__/__init__.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_cache.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_commands.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_fast_tenants.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_middleware.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_multi_types.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_routes.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_settings.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_subfolder_case.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_tenants.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_urlresolvers.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_utils.cpython-312.pyc,,
django_tenants/tests/__pycache__/test_validation_utils.cpython-312.pyc,,
django_tenants/tests/__pycache__/testcases.cpython-312.pyc,,
django_tenants/tests/test_cache.py,sha256=QhCqgRPqb8YryNl4jPHX5yL3aP-QB5hE4Of7l_SuOPs,491
django_tenants/tests/test_commands.py,sha256=vMnAacjN19CIALvBK0grXm2v2F-1nXRmHOozATzM4g8,1362
django_tenants/tests/test_fast_tenants.py,sha256=R3UzHBdlC66HAdFvlSeXmc-eU3c0TxBX5kxtwuwhv0I,588
django_tenants/tests/test_middleware.py,sha256=JyK6wHzbebA_AWUtYBAh14kePhOs1gIA3iMQKnXTTUQ,2276
django_tenants/tests/test_multi_types.py,sha256=0592LNUKVepvZQh7uKJywQ_glCSZLgYuKI43go1C8O4,5629
django_tenants/tests/test_routes.py,sha256=mIFpfMancPmlO3p8SXVBzTgSLCblAgjqWo4mpw74rQ0,6403
django_tenants/tests/test_settings.py,sha256=RoddO-Yi-VfuOuPJOzcSV8048j5UZI65o0_wYEFxGe0,683
django_tenants/tests/test_subfolder_case.py,sha256=TBhWuIJh2gcZNwoSP8GyfpECcVkz7L2Vj0w0zlbqOnw,1759
django_tenants/tests/test_tenants.py,sha256=Tck1dt5AKmaxLUjSuqvKv1sjkZbkVg0-dVLVqN8_2tU,30566
django_tenants/tests/test_urlresolvers.py,sha256=maLxUKzFpw0T1--hF-B1OP4JYK2-sN7RQDPSqhyRqbg,3123
django_tenants/tests/test_utils.py,sha256=278ln0lKA28A_MyjsHGbhcBtvZ_STllxqPn09rYsl0g,1898
django_tenants/tests/test_validation_utils.py,sha256=h7rRV9uWH3fE4OubeJuWJz7yx-qHDR7YzkM2NqxQ7IE,1272
django_tenants/tests/testcases.py,sha256=TKfb7GGbBl1YyVr3rd7yJ4SQGH9yYdQpypkDs9-_UVo,1915
django_tenants/urlresolvers.py,sha256=bjNpLs34IgyF2V6I6Yae6hZjoiT4xtSYKZZfqjCP4BE,3112
django_tenants/utils.py,sha256=f6VEHEQQRvpOcWlpLsnGS66aLFSzfGJz0xOL2b1hX4s,11180
